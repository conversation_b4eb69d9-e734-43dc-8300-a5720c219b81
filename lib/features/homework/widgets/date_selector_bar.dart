import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/app_colors.dart';

/// A horizontal scrollable bar for selecting dates
class DateSelectorBar extends StatefulWidget {
  /// Callback when a date is selected
  final Function(DateTime) onDateSelected;
  
  /// Currently selected date
  final DateTime selectedDate;

  const DateSelectorBar({
    super.key,
    required this.onDateSelected,
    required this.selectedDate,
  });

  @override
  State<DateSelectorBar> createState() => _DateSelectorBarState();
}

class _DateSelectorBarState extends State<DateSelectorBar> {
  late ScrollController _scrollController;
  late List<DateTime> _dates;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _generateDates();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// Generate 14 days starting from today
  void _generateDates() {
    final today = DateTime.now();
    _dates = List.generate(14, (index) {
      return DateTime(today.year, today.month, today.day).add(Duration(days: index));
    });
  }

  /// Get day abbreviation (Mon, Tue, etc.)
  String _getDayAbbreviation(DateTime date) {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return days[date.weekday - 1];
  }

  /// Check if two dates are the same day
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      height: 80.h,
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: ListView.builder(
        controller: _scrollController,
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        itemCount: _dates.length,
        itemBuilder: (context, index) {
          final date = _dates[index];
          final isSelected = _isSameDay(date, widget.selectedDate);
          final isToday = _isSameDay(date, DateTime.now());
          
          return Padding(
            padding: EdgeInsets.only(right: 12.w),
            child: _DateChip(
              date: date,
              dayAbbreviation: _getDayAbbreviation(date),
              isSelected: isSelected,
              isToday: isToday,
              onTap: () => widget.onDateSelected(date),
            ),
          );
        },
      ),
    );
  }
}

/// Individual date chip widget
class _DateChip extends StatelessWidget {
  final DateTime date;
  final String dayAbbreviation;
  final bool isSelected;
  final bool isToday;
  final VoidCallback onTap;

  const _DateChip({
    required this.date,
    required this.dayAbbreviation,
    required this.isSelected,
    required this.isToday,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    // Define colors based on state
    Color backgroundColor;
    Color textColor;
    Color borderColor;
    
    if (isSelected) {
      backgroundColor = theme.colorScheme.primary;
      textColor = theme.colorScheme.onPrimary;
      borderColor = theme.colorScheme.primary;
    } else if (isToday) {
      backgroundColor = isDark ? AppColors.surfaceDark : AppColors.surfaceLight;
      textColor = theme.colorScheme.primary;
      borderColor = theme.colorScheme.primary;
    } else {
      backgroundColor = isDark ? AppColors.surfaceDark : AppColors.surfaceLight;
      textColor = isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight;
      borderColor = isDark ? AppColors.borderDark : AppColors.borderLight;
    }

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 60.w,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: borderColor,
            width: isSelected || isToday ? 2 : 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              dayAbbreviation,
              style: theme.textTheme.labelSmall?.copyWith(
                color: textColor,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              date.day.toString(),
              style: theme.textTheme.titleMedium?.copyWith(
                color: textColor,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
