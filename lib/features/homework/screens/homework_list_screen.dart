import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../../../core/theme/app_colors.dart';
import '../mock/mock_homeworks.dart';
import '../models/homework_model.dart';
import '../widgets/date_selector_bar.dart';
import '../widgets/homework_card.dart';

/// Screen displaying the list of homework assignments
class HomeworkListScreen extends StatefulWidget {
  const HomeworkListScreen({super.key});

  @override
  State<HomeworkListScreen> createState() => _HomeworkListScreenState();
}

class _HomeworkListScreenState extends State<HomeworkListScreen> {
  DateTime _selectedDate = DateTime.now();
  List<HomeworkModel> _filteredHomework = [];

  @override
  void initState() {
    super.initState();
    _filterHomeworkByDate();
  }

  /// Filter homework based on selected date
  void _filterHomeworkByDate() {
    final selectedDay = DateTime(_selectedDate.year, _selectedDate.month, _selectedDate.day);
    
    _filteredHomework = mockHomeworkList.where((homework) {
      // Show homework that is due on the selected date or assigned on the selected date
      final dueDay = homework.dueAt != null 
          ? DateTime(homework.dueAt!.year, homework.dueAt!.month, homework.dueAt!.day)
          : null;
      final assignedDay = DateTime(
        homework.assignedAt.year, 
        homework.assignedAt.month, 
        homework.assignedAt.day,
      );
      
      return dueDay == selectedDay || assignedDay == selectedDay;
    }).toList();
    
    // Sort by due date (null dates go to the end)
    _filteredHomework.sort((a, b) {
      if (a.dueAt == null && b.dueAt == null) return 0;
      if (a.dueAt == null) return 1;
      if (b.dueAt == null) return -1;
      return a.dueAt!.compareTo(b.dueAt!);
    });
  }

  /// Handle date selection
  void _onDateSelected(DateTime date) {
    setState(() {
      _selectedDate = date;
      _filterHomeworkByDate();
    });
  }

  /// Show date range picker
  void _showDateRangePicker() {
    // TODO: Implement date range picker
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Date range picker coming soon!'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Homework',
          style: theme.textTheme.titleLarge,
        ),
        actions: [
          IconButton(
            onPressed: _showDateRangePicker,
            icon: const Icon(Symbols.date_range),
            tooltip: 'Select date range',
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Date selector bar
            DateSelectorBar(
              selectedDate: _selectedDate,
              onDateSelected: _onDateSelected,
            ),
            
            // Homework list
            Expanded(
              child: _filteredHomework.isEmpty
                  ? _EmptyState(selectedDate: _selectedDate)
                  : ListView.builder(
                      padding: EdgeInsets.symmetric(vertical: 8.h),
                      itemCount: _filteredHomework.length,
                      itemBuilder: (context, index) {
                        return HomeworkCard(
                          homework: _filteredHomework[index],
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Empty state widget when no homework is found
class _EmptyState extends StatelessWidget {
  final DateTime selectedDate;

  const _EmptyState({required this.selectedDate});

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final selectedDay = DateTime(date.year, date.month, date.day);
    
    if (selectedDay == today) {
      return 'today';
    } else if (selectedDay == today.add(const Duration(days: 1))) {
      return 'tomorrow';
    } else if (selectedDay == today.subtract(const Duration(days: 1))) {
      return 'yesterday';
    } else {
      const months = [
        'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
      ];
      return '${months[date.month - 1]} ${date.day}';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Symbols.assignment,
              size: 64.sp,
              color: isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight,
            ),
            SizedBox(height: 16.h),
            Text(
              'No homework for ${_formatDate(selectedDate)}',
              style: theme.textTheme.titleMedium?.copyWith(
                color: isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              'Enjoy your free time!',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
