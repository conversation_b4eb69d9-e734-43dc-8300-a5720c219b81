import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../../../core/theme/app_colors.dart';
import '../mock/mock_homeworks.dart';
import '../models/homework_model.dart';
import '../widgets/date_selector_bar.dart';
import '../widgets/homework_card.dart';

/// Screen displaying the list of homework assignments
class HomeworkListScreen extends StatefulWidget {
  const HomeworkListScreen({super.key});

  @override
  State<HomeworkListScreen> createState() => _HomeworkListScreenState();
}

class _HomeworkListScreenState extends State<HomeworkListScreen> {
  DateTime? _selectedDate; // null means 'All'
  Map<String, List<HomeworkModel>> _groupedHomework = {};

  @override
  void initState() {
    super.initState();
    _filterHomeworkByDate();
  }

  /// Filter and group homework based on selected date
  void _filterHomeworkByDate() {
    List<HomeworkModel> filteredHomework;

    if (_selectedDate == null) {
      // Show all homework
      filteredHomework = List.from(mockHomeworkList);
    } else {
      // Filter by specific date
      final selectedDay = DateTime(
        _selectedDate!.year,
        _selectedDate!.month,
        _selectedDate!.day,
      );

      filteredHomework = mockHomeworkList.where((homework) {
        // Show homework that is due on the selected date or assigned on the selected date
        final dueDay = homework.dueAt != null
            ? DateTime(
                homework.dueAt!.year,
                homework.dueAt!.month,
                homework.dueAt!.day,
              )
            : null;
        final assignedDay = DateTime(
          homework.assignedAt.year,
          homework.assignedAt.month,
          homework.assignedAt.day,
        );

        return dueDay == selectedDay || assignedDay == selectedDay;
      }).toList();
    }

    // Sort by due date (latest first, null dates go to the end)
    filteredHomework.sort((a, b) {
      if (a.dueAt == null && b.dueAt == null) return 0;
      if (a.dueAt == null) return 1;
      if (b.dueAt == null) return -1;
      return b.dueAt!.compareTo(a.dueAt!); // Reverse order for latest first
    });

    // Group homework by date
    _groupedHomework = {};
    for (final homework in filteredHomework) {
      final dateKey = _getDateKey(homework);
      if (_groupedHomework[dateKey] == null) {
        _groupedHomework[dateKey] = [];
      }
      _groupedHomework[dateKey]!.add(homework);
    }
  }

  /// Get date key for grouping homework
  String _getDateKey(HomeworkModel homework) {
    final date = homework.dueAt ?? homework.assignedAt;
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final homeworkDay = DateTime(date.year, date.month, date.day);

    if (homeworkDay == today) {
      return 'Today';
    } else if (homeworkDay == today.subtract(const Duration(days: 1))) {
      return 'Yesterday';
    } else if (homeworkDay == today.add(const Duration(days: 1))) {
      return 'Tomorrow';
    } else {
      const months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
      ];
      return '${months[date.month - 1]} ${date.day}, ${date.year}';
    }
  }

  /// Handle date selection
  void _onDateSelected(DateTime? date) {
    setState(() {
      _selectedDate = date;
      _filterHomeworkByDate();
    });
  }

  /// Show date range picker
  void _showDateRangePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _selectedDate != null
          ? DateTimeRange(start: _selectedDate!, end: _selectedDate!)
          : null,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Theme.of(context).colorScheme.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      // For now, just select the start date of the range
      // In a full implementation, you might want to filter by the entire range
      setState(() {
        _selectedDate = picked.start;
        _filterHomeworkByDate();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('Homework', style: theme.textTheme.titleLarge),
        actions: [
          IconButton(
            onPressed: _showDateRangePicker,
            icon: const Icon(Symbols.date_range),
            tooltip: 'Select date range',
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Date selector bar
            DateSelectorBar(
              selectedDate: _selectedDate,
              onDateSelected: _onDateSelected,
            ),

            // Homework list
            Expanded(
              child: _groupedHomework.isEmpty
                  ? _EmptyState(selectedDate: _selectedDate)
                  : _GroupedHomeworkList(groupedHomework: _groupedHomework),
            ),
          ],
        ),
      ),
    );
  }
}

/// Grouped homework list widget
class _GroupedHomeworkList extends StatelessWidget {
  final Map<String, List<HomeworkModel>> groupedHomework;

  const _GroupedHomeworkList({required this.groupedHomework});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final sortedKeys = groupedHomework.keys.toList()..sort(_sortDateKeys);

    return ListView.builder(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      itemCount: sortedKeys.length,
      itemBuilder: (context, index) {
        final dateKey = sortedKeys[index];
        final homeworkList = groupedHomework[dateKey]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Date header
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              child: Text(
                dateKey,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.primary,
                ),
              ),
            ),
            // Homework cards for this date
            ...homeworkList.map((homework) => HomeworkCard(homework: homework)),
            SizedBox(height: 8.h),
          ],
        );
      },
    );
  }

  /// Sort date keys to show most recent first
  int _sortDateKeys(String a, String b) {
    // Custom sorting logic for date keys
    const order = ['Today', 'Tomorrow', 'Yesterday'];

    final aIndex = order.indexOf(a);
    final bIndex = order.indexOf(b);

    if (aIndex != -1 && bIndex != -1) {
      return aIndex.compareTo(bIndex);
    } else if (aIndex != -1) {
      return -1;
    } else if (bIndex != -1) {
      return 1;
    } else {
      // For other dates, sort alphabetically (which works for our format)
      return b.compareTo(a); // Reverse for most recent first
    }
  }
}

/// Empty state widget when no homework is found
class _EmptyState extends StatelessWidget {
  final DateTime? selectedDate;

  const _EmptyState({required this.selectedDate});

  String _formatDate(DateTime? date) {
    if (date == null) return 'all dates';

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final selectedDay = DateTime(date.year, date.month, date.day);

    if (selectedDay == today) {
      return 'today';
    } else if (selectedDay == today.add(const Duration(days: 1))) {
      return 'tomorrow';
    } else if (selectedDay == today.subtract(const Duration(days: 1))) {
      return 'yesterday';
    } else {
      const months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
      ];
      return '${months[date.month - 1]} ${date.day}';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Symbols.assignment,
              size: 64.sp,
              color: isDark
                  ? AppColors.textSecondaryDark
                  : AppColors.textSecondaryLight,
            ),
            SizedBox(height: 16.h),
            Text(
              'No homework for ${_formatDate(selectedDate)}',
              style: theme.textTheme.titleMedium?.copyWith(
                color: isDark
                    ? AppColors.textSecondaryDark
                    : AppColors.textSecondaryLight,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              'Enjoy your free time!',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDark
                    ? AppColors.textSecondaryDark
                    : AppColors.textSecondaryLight,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
